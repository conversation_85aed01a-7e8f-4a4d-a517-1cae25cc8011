using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.Chats.GetChat;

public class GetChatQueryHandler : IRequestHandler<GetChatQuery, Result<ChatDto>>
{
    private readonly IConversationDbContext _dbContext;
    private readonly ISharedUserService _userService;

    public GetChatQueryHandler(IConversationDbContext dbContext, ISharedUserService userService)
    {
        _dbContext = dbContext;
        _userService = userService;
    }

    public async Task<Result<ChatDto>> Handle(GetChatQuery request, CancellationToken cancellationToken)
    {
        var chat = await _dbContext.Chat
            .Include(c => c.Messages)
            .ThenInclude(m => m.Attachments)
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (chat == null)
        {
            return Result.Failure<ChatDto>($"Chat with ID {request.Id} not found");
        }

        // Get unique user IDs from messages
        var userIds = chat.Messages
            .Where(m => m.SenderType == Domain.Chats.SenderType.User && Guid.TryParse(m.SenderId, out _))
            .Select(m => Guid.Parse(m.SenderId))
            .Distinct()
            .ToList();

        // Get user information
        var users = new Dictionary<Guid, string>();
        if (userIds.Any())
        {
            var userList = await _userService.GetUsersByIdsAsync(userIds);
            users = userList.ToDictionary(u => u.Id, u => u.UserName ?? string.Empty);
        }

        // Get reply message contents
        var replyMessageIds = chat.Messages
            .Where(m => m.ReplyToMessageId.HasValue)
            .Select(m => m.ReplyToMessageId!.Value)
            .Distinct()
            .ToList();

        var replyMessages = new Dictionary<Guid, string>();
        if (replyMessageIds.Any())
        {
            replyMessages = chat.Messages
                .Where(m => replyMessageIds.Contains(m.Id))
                .ToDictionary(m => m.Id, m => m.Content);
        }

        return Result.Success(chat.ToDto(users, replyMessages));
    }
}
