using Conversations.Application.Abstractions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.Chats.ListChats;

public class ListChatsQueryHandler(
    IConversationDbContext dbContext,
    ISharedUserService userService
) : IRequestHandler<ListChatsQuery, PagedResult<ChatDto>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;

    public async Task<PagedResult<ChatDto>> Handle(ListChatsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Chat.AsNoTracking();
        if (request.CustomerId.HasValue)
        {
            query = query.Where(c => c.CustomerId == request.CustomerId.Value);
        }
        if (request.AssignedUserId.HasValue)
        {
            query = query.Where(c => c.AssignedUserId == request.AssignedUserId.Value);
        }
        if (request.Status.HasValue)
        {
            query = query.Where(c => c.Status == request.Status.Value);
        }
        if (request.Channel.HasValue)
        {
            query = query.Where(c => c.Channel == request.Channel.Value);
        }
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            query = query.Where(c => c.CustomerName.Contains(request.SearchTerm));
        }
        int pageSize = Math.Min(request.PageSize, 100); // Limit max page size
        int skip = (request.PageNumber - 1) * pageSize;
        query = query.OrderByDescending(c => c.StartedAt);
        query = query.Skip(skip).Take(pageSize);
        query = query
            .Include(c => c.Messages.OrderByDescending(m => m.SentAt).Take(10))
            .ThenInclude(m => m.Attachments);

        var filteredCount = await query.CountAsync(cancellationToken);
        var totalCount = await _dbContext.Chat.CountAsync(cancellationToken);
        var items = await query.ToListAsync(cancellationToken);

        // Get unique user IDs from all messages
        var userIds = items
            .SelectMany(c => c.Messages)
            .Where(m => m.SenderType == Domain.Chats.SenderType.User && Guid.TryParse(m.SenderId, out _))
            .Select(m => Guid.Parse(m.SenderId))
            .Distinct()
            .ToList();

        // Get user information
        var users = new Dictionary<Guid, string>();
        if (userIds.Any())
        {
            var userList = await _userService.GetUsersByIdsAsync(userIds);
            users = userList.ToDictionary(u => u.Id, u => u.UserName ?? string.Empty);
        }

        // Get reply message contents
        var replyMessageIds = items
            .SelectMany(c => c.Messages)
            .Where(m => m.ReplyToMessageId.HasValue)
            .Select(m => m.ReplyToMessageId!.Value)
            .Distinct()
            .ToList();

        var replyMessages = new Dictionary<Guid, string>();
        if (replyMessageIds.Any())
        {
            replyMessages = items
                .SelectMany(c => c.Messages)
                .Where(m => replyMessageIds.Contains(m.Id))
                .ToDictionary(m => m.Id, m => m.Content);
        }

        var chats = items.Select(c => c.ToDto(users, replyMessages)).ToList();
        return new PagedResult<ChatDto>(chats)
        {
            PageNumber = request.PageNumber,
            PageSize = request.PageSize,
            FilteredCount = filteredCount,
            Count = totalCount
        };
    }
}
