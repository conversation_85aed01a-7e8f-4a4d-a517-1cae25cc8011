using Conversations.Domain.Chats;

namespace Conversations.Application.Chats;

public record ChatDto
{
    public Guid Id { get; set; }
    public Guid? CustomerId { get; set; }
    public string CustomerName { get; set; }
    public string Channel { get; set; }
    public string ExternalId { get; set; }
    public string Status { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? EndedAt { get; set; }
    public Guid? AssignedUserId { get; set; }
    public string? Title { get; set; }
    public List<ChatMessageDto> Messages { get; set; } = new();
}

public record ChatMessageDto
{
    public Guid Id { get; set; }
    public Guid ChatId { get; set; }
    public string? ExternalId { get; set; }
    public Guid? ReplyToMessageId { get; set; }
    public string Direction { get; set; }
    public string Content { get; set; }
    public string ContentType { get; set; }
    public string Status { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string SenderId { get; set; }
    public string SenderType { get; set; }
    public string? SenderUserName { get; set; }
    public string? ReplyToContent { get; set; }
    public string? MetaData { get; set; }
    public List<ChatAttachmentDto> Attachments { get; set; } = new();
}

public record ChatAttachmentDto
{
    public Guid Id { get; set; }
    public Guid MessageId { get; set; }
    public string Type { get; set; }
    public string Url { get; set; }
    public string? FileName { get; set; }
    public string? ContentType { get; set; }
    public long? FileSize { get; set; }
}

public static class ChatMappingExtensions
{
    public static ChatDto ToDto(this Chat chat)
    {
        return new ChatDto
        {
            Id = chat.Id,
            CustomerId = chat.CustomerId,
            CustomerName = chat.CustomerName,
            Channel = chat.Channel.ToString(),
            ExternalId = chat.ExternalId,
            Status = chat.Status.ToString(),
            StartedAt = chat.StartedAt,
            EndedAt = chat.EndedAt,
            AssignedUserId = chat.AssignedUserId,
            Title = chat.Title,
            Messages = chat.Messages.Select(m => m.ToDto()).ToList()
        };
    }

    public static ChatDto ToDto(this Chat chat, Dictionary<Guid, string> users, Dictionary<Guid, string> replyMessages)
    {
        return new ChatDto
        {
            Id = chat.Id,
            CustomerId = chat.CustomerId,
            CustomerName = chat.CustomerName,
            Channel = chat.Channel.ToString(),
            ExternalId = chat.ExternalId,
            Status = chat.Status.ToString(),
            StartedAt = chat.StartedAt,
            EndedAt = chat.EndedAt,
            AssignedUserId = chat.AssignedUserId,
            Title = chat.Title,
            Messages = chat.Messages.Select(m => m.ToDto(users, replyMessages)).ToList()
        };
    }

    public static ChatMessageDto ToDto(this ChatMessage message)
    {
        return new ChatMessageDto
        {
            Id = message.Id,
            ChatId = message.ChatId,
            ExternalId = message.ExternalId,
            ReplyToMessageId = message.ReplyToMessageId,
            Direction = message.Direction.ToString(),
            Content = message.Content,
            ContentType = message.ContentType.ToString(),
            Status = message.Status.ToString(),
            SentAt = message.SentAt,
            DeliveredAt = message.DeliveredAt,
            ReadAt = message.ReadAt,
            SenderId = message.SenderId,
            SenderType = message.SenderType.ToString(),
            MetaData = message.MetaData,
            Attachments = message.Attachments.Select(a => a.ToDto()).ToList()
        };
    }

    public static ChatMessageDto ToDto(this ChatMessage message, Dictionary<Guid, string> users, Dictionary<Guid, string> replyMessages)
    {
        string? senderUserName = null;
        if (message.SenderType == Domain.Chats.SenderType.User && Guid.TryParse(message.SenderId, out var userId))
        {
            users.TryGetValue(userId, out senderUserName);
        }

        string? replyToContent = null;
        if (message.ReplyToMessageId.HasValue)
        {
            replyMessages.TryGetValue(message.ReplyToMessageId.Value, out replyToContent);
        }

        return new ChatMessageDto
        {
            Id = message.Id,
            ChatId = message.ChatId,
            ExternalId = message.ExternalId,
            ReplyToMessageId = message.ReplyToMessageId,
            Direction = message.Direction.ToString(),
            Content = message.Content,
            ContentType = message.ContentType.ToString(),
            Status = message.Status.ToString(),
            SentAt = message.SentAt,
            DeliveredAt = message.DeliveredAt,
            ReadAt = message.ReadAt,
            SenderId = message.SenderId,
            SenderType = message.SenderType.ToString(),
            SenderUserName = senderUserName,
            ReplyToContent = replyToContent,
            MetaData = message.MetaData,
            Attachments = message.Attachments.Select(a => a.ToDto()).ToList()
        };
    }

    public static ChatAttachmentDto ToDto(this ChatAttachment attachment)
    {
        return new ChatAttachmentDto
        {
            Id = attachment.Id,
            MessageId = attachment.MessageId,
            Type = attachment.Type.ToString(),
            Url = attachment.Url,
            FileName = attachment.FileName,
            ContentType = attachment.ContentType,
            FileSize = attachment.FileSize
        };
    }
}
